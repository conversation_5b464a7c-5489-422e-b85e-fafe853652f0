import { BaseWebView } from '@/components/BaseWebView/BaseWebView'
import { withAuthentication } from '@/hoc/withAuthentication'
import { WEBVIEW_APP_ROUTES } from '@/routes/webviewRoutes'
import React from 'react'

function HealthScreen() {
  return (
    <BaseWebView
      source={{
        uri: WEBVIEW_APP_ROUTES.MEDICAL_HANDBOOK?.children?.FACULTIES?.path,
      }}
      isTabScreen
      setupFurigana
    />
  )
}

export default withAuthentication(HealthScreen)
