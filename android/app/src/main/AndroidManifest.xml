<manifest xmlns:android="http://schemas.android.com/apk/res/android" xmlns:tools="http://schemas.android.com/tools">
  <uses-permission android:name="android.permission.CAMERA"/>
  <uses-permission android:name="android.permission.INTERNET"/>
  <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS"/>
  <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
  <uses-permission android:name="android.permission.READ_MEDIA_AUDIO"/>
  <uses-permission android:name="android.permission.READ_MEDIA_IMAGES"/>
  <uses-permission android:name="android.permission.READ_MEDIA_VIDEO"/>
  <uses-permission android:name="android.permission.READ_MEDIA_VISUAL_USER_SELECTED"/>
  <uses-permission android:name="android.permission.RECORD_AUDIO"/>
  <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW"/>
  <uses-permission android:name="android.permission.VIBRATE"/>
  <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
  <queries>
    <intent>
      <action android:name="android.intent.action.VIEW"/>
      <category android:name="android.intent.category.BROWSABLE"/>
      <data android:scheme="https"/>
    </intent>
  </queries>
  <application android:name=".MainApplication" android:label="@string/app_name" android:icon="@mipmap/ic_launcher" android:allowBackup="true" android:theme="@style/AppTheme" android:supportsRtl="true" android:enableOnBackInvokedCallback="false" android:requestLegacyExternalStorage="true" android:fullBackupContent="@xml/secure_store_backup_rules" android:dataExtractionRules="@xml/secure_store_data_extraction_rules">
    <meta-data android:name="com.google.firebase.messaging.default_notification_channel_id" tools:replace="android:value" android:value="default"/>
    <meta-data android:name="com.google.firebase.messaging.default_notification_icon" android:resource="@drawable/notification_icon"/>
    <meta-data android:name="expo.modules.notifications.default_notification_icon" android:resource="@drawable/notification_icon"/>
    <meta-data android:name="expo.modules.updates.ENABLED" android:value="true"/>
    <meta-data android:name="expo.modules.updates.EXPO_RUNTIME_VERSION" android:value="@string/expo_runtime_version"/>
    <meta-data android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH" android:value="ALWAYS"/>
    <meta-data android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS" android:value="0"/>
    <meta-data android:name="expo.modules.updates.EXPO_UPDATE_URL" android:value="https://u.expo.dev/d1a2d577-c2ff-4b68-ba1b-f80df20e00e5"/>
    <activity android:name=".MainActivity" android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode" android:launchMode="singleTask" android:windowSoftInputMode="adjustResize" android:theme="@style/Theme.App.SplashScreen" android:exported="true" android:screenOrientation="portrait">
      <intent-filter>
        <action android:name="android.intent.action.VIEW"/>
        <category android:name="android.intent.category.DEFAULT"/>
        <category android:name="android.intent.category.BROWSABLE"/>
        <data android:scheme="com.wap.hico"/>
        <data android:scheme="exp+hico"/>
      </intent-filter>
      <intent-filter android:autoVerify="true" data-generated="true">
        <action android:name="android.intent.action.VIEW"/>
        <data android:scheme="com.wap.hico" android:host="oauthredirect"/>
        <data android:scheme="https" android:host="japanmedicalgate.minastik.com" android:pathPrefix="/"/>
        <data android:scheme="https" android:host="japanmedicalgate.minastik.com" android:pathPrefix="/products"/>
        <data android:scheme="https" android:host="japanmedicalgate.minastik.com" android:pathPrefix="/products-v2"/>
        <data android:scheme="https" android:host="japanmedicalgate.minastik.com" android:pathPrefix="/posts"/>
        <data android:scheme="https" android:host="japanmedicalgate.minastik.com" android:pathPrefix="/medical-dictionary"/>
        <data android:scheme="https" android:host="japanmedicalgate.qa.minastik.com" android:pathPrefix="/"/>
        <data android:scheme="https" android:host="japanmedicalgate.qa.minastik.com" android:pathPrefix="/products"/>
        <data android:scheme="https" android:host="japanmedicalgate.qa.minastik.com" android:pathPrefix="/products-v2"/>
        <data android:scheme="https" android:host="japanmedicalgate.qa.minastik.com" android:pathPrefix="/posts"/>
        <data android:scheme="https" android:host="japanmedicalgate.qa.minastik.com" android:pathPrefix="/medical-dictionary"/>
        <data android:scheme="https" android:host="hico.co.jp" android:pathPrefix="/"/>
        <data android:scheme="https" android:host="hico.co.jp" android:pathPrefix="/products"/>
        <data android:scheme="https" android:host="hico.co.jp" android:pathPrefix="/products-v2"/>
        <data android:scheme="https" android:host="hico.co.jp" android:pathPrefix="/posts"/>
        <data android:scheme="https" android:host="hico.co.jp" android:pathPrefix="/medical-dictionary"/>
        <category android:name="android.intent.category.BROWSABLE"/>
        <category android:name="android.intent.category.DEFAULT"/>
      </intent-filter>
    </activity>
    <activity android:name=".BrowserLauncherActivity" android:theme="@style/Theme.App.SplashScreen" android:exported="true">
      <intent-filter>
        <action android:name="android.intent.action.MAIN"/>
        <category android:name="android.intent.category.LAUNCHER"/>
      </intent-filter>
    </activity>
  </application>
</manifest>